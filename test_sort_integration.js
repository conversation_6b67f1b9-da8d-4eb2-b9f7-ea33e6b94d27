// 测试群聊管理排序功能集成
// 这个文件用于验证 SortableColumn 组件是否正确集成到群聊管理中

console.log('测试群聊管理排序功能集成');

// 模拟测试数据
const mockGroupChatData = [
  {
    id: '1',
    group_name: '测试群聊1',
    member_count: 50,
    created_at: 1640995200000, // 2022-01-01
  },
  {
    id: '2', 
    group_name: '测试群聊2',
    member_count: 30,
    created_at: 1641081600000, // 2022-01-02
  },
  {
    id: '3',
    group_name: '测试群聊3', 
    member_count: 80,
    created_at: 1640908800000, // 2021-12-31
  }
];

// 测试排序功能
function testSortFunction() {
  console.log('原始数据:', mockGroupChatData);
  
  // 测试按成员数排序 (升序)
  const sortedByMemberAsc = [...mockGroupChatData].sort((a, b) => a.member_count - b.member_count);
  console.log('按成员数升序排序:', sortedByMemberAsc);
  
  // 测试按成员数排序 (降序)
  const sortedByMemberDesc = [...mockGroupChatData].sort((a, b) => b.member_count - a.member_count);
  console.log('按成员数降序排序:', sortedByMemberDesc);
  
  // 测试按时间排序 (升序)
  const sortedByTimeAsc = [...mockGroupChatData].sort((a, b) => a.created_at - b.created_at);
  console.log('按时间升序排序:', sortedByTimeAsc);
  
  // 测试按时间排序 (降序)
  const sortedByTimeDesc = [...mockGroupChatData].sort((a, b) => b.created_at - a.created_at);
  console.log('按时间降序排序:', sortedByTimeDesc);
}

// 模拟 handleSort 函数
function mockHandleSort(sortAsc, sortBy) {
  let sort_asc;
  if (sortAsc === 0) {
    sort_asc = 'desc';
  } else if (sortAsc === 1) {
    sort_asc = 'asc';
  } else {
    sort_asc = '';
  }
  
  console.log(`排序参数: sortBy=${sortBy}, sort_asc=${sort_asc}`);
  
  // 模拟 API 调用参数
  const apiParams = {
    sort_by: sortBy,
    sort_asc: sort_asc,
  };
  
  console.log('API 调用参数:', apiParams);
  return apiParams;
}

// 运行测试
console.log('=== 开始测试排序功能 ===');
testSortFunction();

console.log('\n=== 测试 handleSort 函数 ===');
// 测试不同的排序参数组合
mockHandleSort(-1, 1); // 重置排序
mockHandleSort(0, 1);  // 按成员数降序
mockHandleSort(1, 1);  // 按成员数升序
mockHandleSort(0, 2);  // 按时间降序
mockHandleSort(1, 2);  // 按时间升序

console.log('\n=== 排序功能集成测试完成 ===');
